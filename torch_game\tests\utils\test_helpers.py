"""
测试辅助工具类
提供测试过程中需要的各种辅助功能
"""

import os
import sys
import time
import logging
import tempfile
from typing import Dict, Any, Optional, Tuple
from unittest.mock import Mock, MagicMock
import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils import ConfigManager


class TestConfig:
    """测试配置类"""
    
    def __init__(self):
        self.test_config = {
            'env': {
                'max_steps': 20,  # 测试时使用较小的步数
                'n_colors': 6,
                'storage_capacity': 7
            },
            'agent': {
                'lr': 1e-3,
                'gamma': 0.99,
                'epsilon': 0.2,
                'hidden_dim': 64  # 测试时使用较小的网络
            },
            'training': {
                'total_episodes': 5,  # 测试时使用较少的回合
                'update_interval': 10,
                'batch_size': 8,
                'n_epochs': 2
            },
            'game_assistant': {
                'window_name': "最强祖师",
                'model_path': "models/sanxiao/best.pt",
                'main_area': [285, 100, 1230, 625],
                'storage_area': [405, 750, 1185, 895]
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.test_config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def override(self, overrides: Dict[str, Any]):
        """覆盖配置值"""
        for key, value in overrides.items():
            keys = key.split('.')
            config = self.test_config
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value


class MockGameAssistant:
    """模拟游戏助手类，用于单元测试"""

    def __init__(self):
        self.hwnd = 12345
        self.is_running = False
        self.current_state = "start_screen"
        self.blocks_data = {}
        self.call_count = {
            'ensure_game_ready': 0,
            'screenshot': 0,
            'detect_current_game_state': 0,
            'analyze_blocks': 0,
            'click_block': 0
        }

        # 添加缺失的属性
        self.main_area = [285, 100, 1230, 625]
        self.storage_area = [405, 750, 1185, 895]

        # 模拟的截图对象
        self.screenshot = Mock()
        self.screenshot.capture_client_area.return_value = Mock()

        # 模拟的检测对象
        self.detection = Mock()
        self.image_search = Mock()
        
    def ensure_game_ready(self) -> bool:
        """确保游戏准备就绪"""
        self.call_count['ensure_game_ready'] += 1
        self.current_state = "in_game"
        return True
    
    def detect_current_game_state(self, img=None) -> Tuple[str, Optional[Dict]]:
        """检测当前游戏状态"""
        self.call_count['detect_current_game_state'] += 1
        return self.current_state, None
    
    def analyze_blocks(self, img=None) -> Dict[str, Any]:
        """分析方块"""
        self.call_count['analyze_blocks'] += 1
        # 返回模拟的方块数据
        return {
            'main_blocks': {
                f'block_{i}': {
                    'color': f'color_{i % 6}',
                    'position': (100 + i * 50, 100 + (i // 8) * 50),
                    'confidence': 0.9
                } for i in range(20)
            },
            'storage_blocks': {
                f'storage_{i}': {
                    'color': f'color_{i % 6}',
                    'position': (400 + i * 100, 750),
                    'confidence': 0.9
                } for i in range(3)
            }
        }
    
    def click_block(self, block_id: str) -> bool:
        """点击方块"""
        self.call_count['click_block'] += 1
        # 模拟点击成功
        return True
    
    def is_game_ready_for_play(self) -> bool:
        """检查游戏是否准备就绪"""
        return self.current_state == "in_game"
    
    def reset_call_count(self):
        """重置调用计数"""
        for key in self.call_count:
            self.call_count[key] = 0


class TestGameAssistant:
    """真实游戏助手的测试包装类"""
    
    def __init__(self, use_real_game: bool = False):
        self.use_real_game = use_real_game
        self.real_assistant = None
        self.mock_assistant = MockGameAssistant()
        
        if use_real_game:
            self._init_real_assistant()
    
    def _init_real_assistant(self):
        """初始化真实游戏助手"""
        try:
            # 导入真实的游戏助手
            from s4_三消 import TripleEliminationGameAssistant
            from assist import AssistBasicToolkit
            
            assist = AssistBasicToolkit()
            hwnd = assist.register_and_bind_window_objects('最强祖师')
            
            # 检查模型文件是否存在
            model_path = r"models\sanxiao\best.pt"
            if os.path.exists(model_path):
                assist.detection.load_model(model_path)
                self.real_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
                logging.info("真实游戏助手初始化成功")
            else:
                logging.warning(f"模型文件不存在: {model_path}，使用模拟助手")
                self.use_real_game = False
                
        except Exception as e:
            logging.warning(f"无法初始化真实游戏助手: {e}，使用模拟助手")
            self.use_real_game = False
    
    def get_assistant(self):
        """获取游戏助手实例"""
        if self.use_real_game and self.real_assistant:
            return self.real_assistant
        return self.mock_assistant
    
    def is_real_game_available(self) -> bool:
        """检查真实游戏是否可用"""
        return self.use_real_game and self.real_assistant is not None


def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_logs.log')
        ]
    )


def create_temp_config_file(config_data: Dict[str, Any]) -> str:
    """创建临时配置文件"""
    import yaml
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
    yaml.dump(config_data, temp_file, default_flow_style=False)
    temp_file.close()
    
    return temp_file.name


def cleanup_temp_files(*file_paths):
    """清理临时文件"""
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logging.warning(f"清理临时文件失败 {file_path}: {e}")


def measure_execution_time(func):
    """测量函数执行时间的装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"{func.__name__} 执行时间: {execution_time:.2f}秒")
        return result, execution_time
    return wrapper


def check_gpu_availability() -> bool:
    """检查GPU是否可用"""
    return torch.cuda.is_available()


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import platform
    import psutil
    
    return {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'gpu_available': check_gpu_availability(),
        'torch_version': torch.__version__
    }
