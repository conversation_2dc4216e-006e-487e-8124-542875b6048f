#!/usr/bin/env python3
"""
集成测试演示脚本
展示如何使用测试框架进行真实游戏集成测试
"""

import os
import sys
import time
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from torch_game.tests.utils import (
    TestGameAssistant, MockGameAssistant, TestConfig,
    create_test_config, create_test_env, create_test_agent,
    validate_game_state, validate_training_results,
    setup_test_logging, measure_execution_time
)


def demo_mock_integration():
    """演示模拟环境集成测试"""
    print("\n" + "="*60)
    print("🎮 模拟环境集成测试演示")
    print("="*60)
    
    # 设置测试配置
    config = create_test_config({
        'env.max_steps': 15,
        'agent.hidden_dim': 64,
        'training.total_episodes': 3
    })
    
    # 创建模拟游戏助手
    mock_assistant = MockGameAssistant()
    print(f"✅ 创建模拟游戏助手，句柄: {mock_assistant.hwnd}")
    
    # 创建环境
    env = create_test_env(game_assistant=mock_assistant, config=config)
    print(f"✅ 创建测试环境，观察空间: {env.observation_space.shape}")
    
    # 创建智能体
    agent = create_test_agent(env=env, config=config)
    print(f"✅ 创建测试智能体，设备: {agent.device}")
    
    # 测试环境重置
    observation = env.reset()
    print(f"✅ 环境重置成功，观察值形状: {observation.shape}")
    
    # 测试智能体交互
    total_reward = 0
    for step in range(5):
        action, log_prob = agent.select_action(observation.reshape(-1))
        next_obs, reward, done, info = env.step(action)
        
        total_reward += reward
        observation = next_obs
        
        print(f"   步骤 {step+1}: 动作={action}, 奖励={reward:.2f}, 完成={done}")
        
        if done:
            observation = env.reset()
    
    print(f"✅ 交互测试完成，总奖励: {total_reward:.2f}")
    
    # 验证调用次数
    print(f"📊 游戏助手调用统计:")
    for method, count in mock_assistant.call_count.items():
        print(f"   {method}: {count} 次")
    
    # 清理
    env.close()
    print("✅ 模拟环境集成测试完成")


def demo_real_game_integration():
    """演示真实游戏集成测试"""
    print("\n" + "="*60)
    print("🎯 真实游戏集成测试演示")
    print("="*60)
    
    # 尝试连接真实游戏
    test_assistant = TestGameAssistant(use_real_game=True)
    
    if not test_assistant.is_real_game_available():
        print("⚠️  真实游戏环境不可用，跳过真实游戏测试")
        print("   请确保：")
        print("   1. 游戏窗口 '最强祖师' 已打开")
        print("   2. 模型文件 'models/sanxiao/best.pt' 存在")
        return
    
    game_assistant = test_assistant.get_assistant()
    print(f"✅ 连接真实游戏成功，窗口句柄: {game_assistant.hwnd}")
    
    # 测试截图功能
    try:
        img = game_assistant.screenshot.capture_client_area()
        if img is not None:
            print("✅ 截图功能正常")
        else:
            print("❌ 截图功能异常")
    except Exception as e:
        print(f"❌ 截图测试失败: {e}")
    
    # 测试游戏状态检测
    try:
        state, result = game_assistant.detect_current_game_state()
        print(f"✅ 游戏状态检测: {state}")
    except Exception as e:
        print(f"❌ 状态检测失败: {e}")
    
    # 测试方块分析
    try:
        blocks_data = game_assistant.analyze_blocks()
        is_valid, errors = validate_game_state(blocks_data)
        
        if is_valid:
            main_blocks = len(blocks_data.get('main_blocks', {}))
            storage_blocks = len(blocks_data.get('storage_blocks', {}))
            print(f"✅ 方块分析成功: 主区域 {main_blocks} 个，预存区 {storage_blocks} 个")
        else:
            print(f"⚠️  方块数据验证警告: {errors}")
    except Exception as e:
        print(f"❌ 方块分析失败: {e}")
    
    print("✅ 真实游戏集成测试完成")


@measure_execution_time
def demo_performance_testing():
    """演示性能测试"""
    print("\n" + "="*60)
    print("⚡ 性能测试演示")
    print("="*60)
    
    config = create_test_config({
        'agent.hidden_dim': 128,
        'training.batch_size': 16
    })
    
    # 测试环境创建性能
    print("📊 测试环境创建性能...")
    creation_times = []
    
    for i in range(5):
        start_time = time.time()
        
        mock_assistant = MockGameAssistant()
        env = create_test_env(game_assistant=mock_assistant, config=config)
        
        creation_time = time.time() - start_time
        creation_times.append(creation_time)
        
        env.close()
        print(f"   试验 {i+1}: {creation_time:.4f}秒")
    
    avg_creation_time = sum(creation_times) / len(creation_times)
    print(f"✅ 平均环境创建时间: {avg_creation_time:.4f}秒")
    
    # 测试智能体推理性能
    print("📊 测试智能体推理性能...")
    
    env = create_test_env(config=config)
    agent = create_test_agent(env=env, config=config)
    
    # 预热
    test_obs = env.reset()
    for _ in range(3):
        agent.select_action(test_obs.reshape(-1))
    
    # 性能测试
    inference_times = []
    for i in range(20):
        start_time = time.time()
        action, _ = agent.select_action(test_obs.reshape(-1))
        inference_time = time.time() - start_time
        inference_times.append(inference_time)
    
    avg_inference_time = sum(inference_times) / len(inference_times)
    throughput = 1.0 / avg_inference_time
    
    print(f"✅ 平均推理时间: {avg_inference_time:.4f}秒")
    print(f"✅ 推理吞吐量: {throughput:.2f} 次/秒")
    
    env.close()
    print("✅ 性能测试完成")


def demo_training_validation():
    """演示训练验证"""
    print("\n" + "="*60)
    print("🎓 训练验证演示")
    print("="*60)
    
    config = create_test_config({
        'training.total_episodes': 3,
        'training.update_interval': 10,
        'env.max_steps': 15
    })
    
    # 创建训练组件
    env = create_test_env(config=config)
    agent = create_test_agent(env=env, config=config)
    
    print("✅ 训练组件创建完成")
    
    # 模拟训练过程
    episode_rewards = []
    
    for episode in range(config.get('training.total_episodes')):
        observation = env.reset()
        episode_reward = 0
        
        for step in range(config.get('env.max_steps')):
            action, _ = agent.select_action(observation.reshape(-1))
            next_obs, reward, done, info = env.step(action)
            
            episode_reward += reward
            observation = next_obs
            
            if done:
                break
        
        episode_rewards.append(episode_reward)
        print(f"   回合 {episode+1}: 奖励 {episode_reward:.2f}")
    
    # 验证训练结果
    training_results = {
        'avg_reward': sum(episode_rewards) / len(episode_rewards),
        'total_episodes': len(episode_rewards),
        'avg_length': config.get('env.max_steps')
    }
    
    is_valid, errors = validate_training_results(training_results, min_episodes=1)
    
    if is_valid:
        print(f"✅ 训练结果验证通过: {training_results}")
    else:
        print(f"❌ 训练结果验证失败: {errors}")
    
    env.close()
    print("✅ 训练验证完成")


def main():
    """主函数"""
    print("🎮 Torch Game 集成测试演示")
    print("本演示展示了如何使用测试框架进行各种类型的测试")
    
    # 设置日志
    setup_test_logging()
    
    try:
        # 1. 模拟环境集成测试
        demo_mock_integration()
        
        # 2. 真实游戏集成测试
        demo_real_game_integration()
        
        # 3. 性能测试
        performance_result, performance_time = demo_performance_testing()
        print(f"⏱️  性能测试总耗时: {performance_time:.2f}秒")
        
        # 4. 训练验证
        demo_training_validation()
        
        print("\n" + "="*60)
        print("🎉 所有演示测试完成！")
        print("="*60)
        
        print("\n📋 下一步建议:")
        print("1. 运行完整测试套件: python scripts/run_tests.py --type all")
        print("2. 查看测试文档: torch_game/docs/测试指南.md")
        print("3. 编写自定义测试用例")
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
