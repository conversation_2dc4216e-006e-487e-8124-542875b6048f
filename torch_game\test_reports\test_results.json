{"smoke": {"test_type": "smoke_tests", "return_code": 1, "duration": 4.042929649353027, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.10.10, pytest-8.4.1, pluggy-1.6.0\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nrootdir: E:\\xy2_yolo_auxiliary\nconfigfile: pytest.ini\nplugins: anyio-4.8.0, benchmark-5.1.0, cov-6.2.1, html-4.1.1, metadata-3.1.1\ncollected 66 items / 14 deselected / 52 selected\n\ntests\\integration\\test_end_to_end.py .F\n\n================================== FAILURES ===================================\n__________________ TestEndToEnd.test_short_training_pipeline __________________\ntests\\utils\\test_helpers.py:232: in wrapper\n    result = func(*args, **kwargs)\ntests\\integration\\test_end_to_end.py:109: in test_short_training_pipeline\n    self.trainer.train()\ncore\\utils\\trainer.py:81: in train\n    self.collect_rollout()\ncore\\utils\\trainer.py:132: in collect_rollout\n    value = self.agent.ac.critic(\nD:\\soft\\Python\\Python310\\lib\\site-packages\\torch\\nn\\modules\\module.py:1130: in _call_impl\n    return forward_call(*input, **kwargs)\nD:\\soft\\Python\\Python310\\lib\\site-packages\\torch\\nn\\modules\\container.py:139: in forward\n    input = module(input)\nD:\\soft\\Python\\Python310\\lib\\site-packages\\torch\\nn\\modules\\module.py:1130: in _call_impl\n    return forward_call(*input, **kwargs)\nD:\\soft\\Python\\Python310\\lib\\site-packages\\torch\\nn\\modules\\linear.py:114: in forward\n    return F.linear(input, self.weight, self.bias)\nE   RuntimeError: mat1 and mat2 shapes cannot be multiplied (1x53 and 64x64)\n---------------------------- Captured stdout call -----------------------------\n开始训练...\n正在确保游戏准备就绪...\n游戏已准备就绪\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n检查游戏结束状态时出错: 'MockGameAssistant' object has no attribute 'check_and_handle_game_state'\n获取方块状态时出错: 'MockGameAssistant' object has no attribute 'main_area'\n---------------------------- Captured stderr call -----------------------------\n2025-06-26 01:35:55,360 - torch_game.tests.integration.test_end_to_end - INFO - \\ufffd\\ufffd\\u02bc\\ufffd\\ufffd\\ufffd\\u0536\\ufffd\\ufffd\\ufffd\\u0475\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd...\\r\n------------------------------ Captured log call ------------------------------\nINFO     torch_game.tests.integration.test_end_to_end:test_end_to_end.py:94 开始测试短期训练流程...\n-------------------------- Captured stderr teardown ---------------------------\n2025-06-26 01:35:56,097 - root - WARNING - \\ufffd\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd\\u02b1\\ufffd\\u013c\\ufffd\\u02a7\\ufffd\\ufffd C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5i48_khc: [WinError 5] \\ufffd\\u073e\\ufffd\\ufffd\\ufffd\\ufffd\\u02a1\\ufffd: 'C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\AppData\\\\\\\\Local\\\\\\\\Temp\\\\\\\\tmp5i48_khc'\\r\n---------------------------- Captured log teardown ----------------------------\nWARNING  root:test_helpers.py:225 清理临时文件失败 C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5i48_khc: [WinError 5] 拒绝访问。: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp5i48_khc'\n- generated xml file: E:\\xy2_yolo_auxiliary\\torch_game\\test_reports\\smoke_tests.xml -\n=========================== short test summary info ===========================\nFAILED tests\\integration\\test_end_to_end.py::TestEndToEnd::test_short_training_pipeline\n!!!!!!!!!!!!!!!!!!!!!!!!!! stopping after 1 failures !!!!!!!!!!!!!!!!!!!!!!!!!!\n================= 1 failed, 1 passed, 14 deselected in 1.10s ==================\n", "stderr": "", "summary": "================= 1 failed, 1 passed, 14 deselected in 1.10s ==================", "success": false}}