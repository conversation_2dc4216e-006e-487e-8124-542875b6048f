"""
真实游戏集成测试
测试与真实游戏窗口的完整集成流程
"""

import pytest
import os
import sys
import time
import logging
from typing import Dict, Any, Optional
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.tests.utils import (
    TestGameAssistant, TestConfig, create_test_config,
    validate_game_state, validate_observation, validate_action,
    setup_test_logging, measure_execution_time
)


class TestRealGameIntegration:
    """真实游戏集成测试类"""
    
    @classmethod
    def setup_class(cls):
        """类级别的设置"""
        setup_test_logging()
        cls.logger = logging.getLogger(__name__)
        cls.config = create_test_config()
        
        # 尝试初始化真实游戏助手
        cls.test_assistant = TestGameAssistant(use_real_game=True)
        cls.real_game_available = cls.test_assistant.is_real_game_available()
        
        if not cls.real_game_available:
            cls.logger.warning("真实游戏不可用，将跳过相关测试")
    
    def setup_method(self):
        """每个测试方法前的设置"""
        if not self.real_game_available:
            pytest.skip("真实游戏不可用")
        
        self.game_assistant = self.test_assistant.get_assistant()
        self.env = None
        self.agent = None
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.env:
            self.env.close()
        if hasattr(self, 'game_assistant') and self.game_assistant:
            # 清理游戏助手资源
            pass
    
    def test_game_window_detection(self):
        """测试游戏窗口检测"""
        self.logger.info("开始测试游戏窗口检测...")
        
        # 检查游戏窗口是否存在
        assert self.game_assistant.hwnd != 0, "未找到游戏窗口"
        
        # 检查窗口操作对象
        assert hasattr(self.game_assistant, 'window_ops'), "缺少窗口操作对象"
        assert hasattr(self.game_assistant, 'screenshot'), "缺少截图对象"
        assert hasattr(self.game_assistant, 'detection'), "缺少检测对象"
        
        self.logger.info(f"游戏窗口检测成功，句柄: {self.game_assistant.hwnd}")
    
    def test_screenshot_capture(self):
        """测试截图功能"""
        self.logger.info("开始测试截图功能...")
        
        # 捕获截图
        img = self.game_assistant.screenshot.capture_client_area()
        
        # 验证截图
        assert img is not None, "截图失败"
        
        # 如果是PIL图像，检查其属性
        if hasattr(img, 'size'):
            width, height = img.size
            assert width > 0 and height > 0, f"截图尺寸无效: {width}x{height}"
            self.logger.info(f"截图成功，尺寸: {width}x{height}")
        
        # 如果是numpy数组，检查其形状
        elif hasattr(img, 'shape'):
            assert len(img.shape) >= 2, f"截图形状无效: {img.shape}"
            self.logger.info(f"截图成功，形状: {img.shape}")
    
    @measure_execution_time
    def test_game_state_detection(self):
        """测试游戏状态检测"""
        self.logger.info("开始测试游戏状态检测...")
        
        # 捕获当前截图
        img = self.game_assistant.screenshot.capture_client_area()
        assert img is not None, "无法捕获截图"
        
        # 检测游戏状态
        state, result = self.game_assistant.detect_current_game_state(img)
        
        # 验证状态检测结果
        assert isinstance(state, str), f"游戏状态应该是字符串，实际类型: {type(state)}"
        assert state in ['start_screen', 'in_game', 'game_over', 'success'], f"未知的游戏状态: {state}"
        
        self.logger.info(f"游戏状态检测成功: {state}")
        
        return state, result
    
    @measure_execution_time
    def test_block_analysis(self):
        """测试方块分析功能"""
        self.logger.info("开始测试方块分析功能...")
        
        # 确保游戏准备就绪
        ready = self.game_assistant.ensure_game_ready()
        if not ready:
            pytest.skip("游戏未准备就绪")
        
        # 捕获截图
        img = self.game_assistant.screenshot.capture_client_area()
        assert img is not None, "无法捕获截图"
        
        # 分析方块
        blocks_data = self.game_assistant.analyze_blocks(img)
        
        # 验证方块数据
        is_valid, errors = validate_game_state(blocks_data)
        if not is_valid:
            self.logger.warning(f"方块数据验证失败: {errors}")
        
        # 检查基本结构
        assert isinstance(blocks_data, dict), "方块数据应该是字典"
        
        if 'main_blocks' in blocks_data:
            main_blocks = blocks_data['main_blocks']
            self.logger.info(f"检测到主区域方块数量: {len(main_blocks)}")
        
        if 'storage_blocks' in blocks_data:
            storage_blocks = blocks_data['storage_blocks']
            self.logger.info(f"检测到预存区方块数量: {len(storage_blocks)}")
        
        return blocks_data
    
    def test_game_ready_check(self):
        """测试游戏准备检查"""
        self.logger.info("开始测试游戏准备检查...")
        
        # 检查游戏是否准备就绪
        is_ready = self.game_assistant.is_game_ready_for_play()
        self.logger.info(f"游戏准备状态: {is_ready}")
        
        # 如果未准备就绪，尝试确保游戏准备
        if not is_ready:
            self.logger.info("尝试确保游戏准备就绪...")
            success = self.game_assistant.ensure_game_ready()
            self.logger.info(f"游戏准备操作结果: {success}")
            
            # 再次检查
            is_ready_after = self.game_assistant.is_game_ready_for_play()
            self.logger.info(f"操作后游戏准备状态: {is_ready_after}")
    
    def test_environment_creation(self):
        """测试环境创建"""
        self.logger.info("开始测试环境创建...")
        
        # 创建环境
        self.env = OptimizedMatch3Env(
            game_assistant=self.game_assistant,
            max_steps=self.config.get('env.max_steps', 20),
            n_colors=self.config.get('env.n_colors', 6),
            storage_capacity=self.config.get('env.storage_capacity', 7)
        )
        
        # 验证环境属性
        assert self.env.game_assistant is not None, "环境的游戏助手为空"
        assert self.env.max_steps > 0, "最大步数应该大于0"
        assert self.env.n_colors > 0, "颜色数量应该大于0"
        assert self.env.storage_capacity > 0, "预存区容量应该大于0"
        
        # 检查观察空间和动作空间
        assert self.env.observation_space is not None, "观察空间未定义"
        assert self.env.action_space is not None, "动作空间未定义"
        
        self.logger.info(f"环境创建成功，观察空间: {self.env.observation_space.shape}, 动作空间: {self.env.action_space.n}")
    
    def test_environment_reset(self):
        """测试环境重置"""
        self.logger.info("开始测试环境重置...")
        
        # 创建环境
        self.test_environment_creation()
        
        # 重置环境
        observation = self.env.reset()
        
        # 验证观察值
        is_valid, errors = validate_observation(
            observation, 
            expected_shape=self.env.observation_space.shape
        )
        assert is_valid, f"观察值验证失败: {errors}"
        
        # 检查环境状态
        assert self.env.current_step == 0, "重置后当前步数应该为0"
        assert self.env.total_reward == 0, "重置后总奖励应该为0"
        
        self.logger.info(f"环境重置成功，观察值形状: {observation.shape}")
    
    def test_environment_step(self):
        """测试环境步进"""
        self.logger.info("开始测试环境步进...")
        
        # 重置环境
        self.test_environment_reset()
        
        # 执行几个步骤
        for step in range(3):
            # 选择随机动作
            action = self.env.action_space.sample()
            
            # 验证动作
            is_valid, errors = validate_action(action, self.env.action_space.n)
            assert is_valid, f"动作验证失败: {errors}"
            
            # 执行步骤
            next_obs, reward, done, info = self.env.step(action)
            
            # 验证返回值
            is_valid, errors = validate_observation(
                next_obs, 
                expected_shape=self.env.observation_space.shape
            )
            assert is_valid, f"下一个观察值验证失败: {errors}"
            
            assert isinstance(reward, (int, float)), f"奖励应该是数值，实际类型: {type(reward)}"
            assert isinstance(done, bool), f"done应该是布尔值，实际类型: {type(done)}"
            assert isinstance(info, dict), f"info应该是字典，实际类型: {type(info)}"
            
            self.logger.info(f"步骤 {step}: 动作={action}, 奖励={reward:.2f}, 完成={done}")
            
            if done:
                break
    
    def test_agent_integration(self):
        """测试智能体集成"""
        self.logger.info("开始测试智能体集成...")
        
        # 创建环境
        self.test_environment_creation()
        
        # 创建智能体
        state_dim = np.prod(self.env.observation_space.shape)
        action_dim = self.env.action_space.n
        
        self.agent = PPOAgent(
            state_dim=state_dim,
            action_dim=action_dim,
            lr=self.config.get('agent.lr', 1e-3),
            hidden_dim=self.config.get('agent.hidden_dim', 64),
            device='cpu'  # 测试时使用CPU
        )
        
        # 重置环境
        observation = self.env.reset()
        
        # 智能体选择动作
        action, log_prob = self.agent.select_action(observation.reshape(-1))
        
        # 验证动作
        is_valid, errors = validate_action(action, action_dim)
        assert is_valid, f"智能体动作验证失败: {errors}"
        
        # 执行动作
        next_obs, reward, done, info = self.env.step(action)
        
        self.logger.info(f"智能体集成测试成功，动作: {action}, 奖励: {reward:.2f}")
    
    @pytest.mark.slow
    def test_complete_episode(self):
        """测试完整回合"""
        self.logger.info("开始测试完整回合...")
        
        # 创建环境和智能体
        self.test_agent_integration()
        
        # 执行完整回合
        observation = self.env.reset()
        total_reward = 0
        step_count = 0
        max_steps = 10  # 限制测试步数
        
        while step_count < max_steps:
            # 智能体选择动作
            action, _ = self.agent.select_action(observation.reshape(-1))
            
            # 执行动作
            next_obs, reward, done, info = self.env.step(action)
            
            total_reward += reward
            step_count += 1
            observation = next_obs
            
            self.logger.info(f"步骤 {step_count}: 动作={action}, 奖励={reward:.2f}, 累计奖励={total_reward:.2f}")
            
            if done:
                break
        
        self.logger.info(f"完整回合测试完成，总步数: {step_count}, 总奖励: {total_reward:.2f}")
        
        # 验证回合结果
        assert step_count > 0, "回合步数应该大于0"
        assert isinstance(total_reward, (int, float)), "总奖励应该是数值"
