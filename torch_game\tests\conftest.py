"""
pytest配置文件
定义全局测试配置、夹具和钩子
"""

import pytest
import os
import sys
import tempfile
import shutil
import logging
from typing import Dict, Any
import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from torch_game.tests.utils import (
    TestConfig, create_test_config, setup_test_logging,
    TestGameAssistant, MockGameAssistant, cleanup_temp_files
)


def pytest_configure(config):
    """pytest配置钩子"""
    # 设置测试日志
    setup_test_logging()
    
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )
    config.addinivalue_line(
        "markers", "real_game: marks tests that require real game environment"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为集成测试添加标记
    for item in items:
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        
        if "real_game" in item.name or "real_game" in str(item.fspath):
            item.add_marker(pytest.mark.real_game)


@pytest.fixture(scope="session")
def test_session_config():
    """会话级别的测试配置"""
    return create_test_config({
        'env.max_steps': 20,
        'env.n_colors': 6,
        'env.storage_capacity': 7,
        'agent.hidden_dim': 64,
        'agent.lr': 1e-3,
        'training.total_episodes': 3,
        'training.batch_size': 8,
        'training.update_interval': 10
    })


@pytest.fixture(scope="session")
def temp_directory():
    """会话级别的临时目录"""
    temp_dir = tempfile.mkdtemp(prefix="torch_game_test_")
    yield temp_dir
    # 清理
    cleanup_temp_files(temp_dir)


@pytest.fixture(scope="function")
def test_config():
    """函数级别的测试配置"""
    return create_test_config()


@pytest.fixture(scope="function")
def mock_game_assistant():
    """模拟游戏助手夹具"""
    assistant = MockGameAssistant()
    yield assistant
    # 清理
    assistant.reset_call_count()


@pytest.fixture(scope="function")
def real_game_assistant():
    """真实游戏助手夹具"""
    test_assistant = TestGameAssistant(use_real_game=True)
    if not test_assistant.is_real_game_available():
        pytest.skip("真实游戏环境不可用")
    
    yield test_assistant.get_assistant()
    # 清理资源


@pytest.fixture(scope="function")
def test_environment(mock_game_assistant, test_config):
    """测试环境夹具"""
    from torch_game.tests.utils import create_test_env
    
    env = create_test_env(game_assistant=mock_game_assistant, config=test_config)
    yield env
    env.close()


@pytest.fixture(scope="function")
def test_agent(test_environment, test_config):
    """测试智能体夹具"""
    from torch_game.tests.utils import create_test_agent
    
    agent = create_test_agent(env=test_environment, config=test_config)
    yield agent
    # 智能体会自动清理


@pytest.fixture(scope="function")
def test_trainer(test_environment, test_agent, test_config, temp_directory):
    """测试训练器夹具"""
    from torch_game.tests.utils import create_test_trainer
    from torch_game.core.utils.trainer import Trainer
    
    trainer = Trainer(
        env=test_environment,
        agent=test_agent,
        n_episodes=test_config.get('training.total_episodes', 3),
        update_interval=test_config.get('training.update_interval', 10),
        batch_size=test_config.get('training.batch_size', 8),
        n_epochs=2,
        log_interval=1,
        eval_interval=2,
        save_interval=5,
        save_dir=temp_directory
    )
    
    yield trainer
    
    # 清理
    if hasattr(trainer, 'writer') and trainer.writer:
        trainer.writer.close()


@pytest.fixture(scope="function")
def performance_config():
    """性能测试配置"""
    return create_test_config({
        'env.max_steps': 50,
        'env.n_colors': 6,
        'env.storage_capacity': 7,
        'agent.hidden_dim': 128,
        'agent.lr': 3e-4,
        'training.batch_size': 32,
        'training.update_interval': 100
    })


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # 设置日志级别
    logging.getLogger('torch_game').setLevel(logging.INFO)
    
    yield
    
    # 测试后清理
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


@pytest.fixture(scope="function")
def capture_logs():
    """捕获日志输出"""
    import io
    from contextlib import redirect_stderr, redirect_stdout
    
    log_capture = io.StringIO()
    
    with redirect_stdout(log_capture), redirect_stderr(log_capture):
        yield log_capture
    
    # 可以在测试中访问 log_capture.getvalue() 获取日志内容


def pytest_runtest_setup(item):
    """测试运行前的设置"""
    # 检查标记并跳过相应测试
    if item.get_closest_marker("real_game"):
        # 检查真实游戏环境是否可用
        test_assistant = TestGameAssistant(use_real_game=True)
        if not test_assistant.is_real_game_available():
            pytest.skip("真实游戏环境不可用")


def pytest_runtest_teardown(item, nextitem):
    """测试运行后的清理"""
    # 强制垃圾回收
    import gc
    gc.collect()
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


@pytest.fixture(scope="function")
def benchmark_results():
    """基准测试结果收集器"""
    results = {}
    yield results
    
    # 可以在这里保存基准测试结果到文件
    if results:
        import json
        import datetime
        
        timestamp = datetime.datetime.now().isoformat()
        results['timestamp'] = timestamp
        results['system_info'] = {
            'python_version': sys.version,
            'torch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available()
        }
        
        # 保存到临时文件（实际使用中可以保存到指定位置）
        benchmark_file = f"/tmp/benchmark_results_{timestamp.replace(':', '-')}.json"
        try:
            with open(benchmark_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"基准测试结果已保存到: {benchmark_file}")
        except Exception as e:
            print(f"保存基准测试结果失败: {e}")


# 自定义断言帮助函数
def assert_performance_within_threshold(actual_value, expected_value, threshold_percent=20):
    """断言性能在阈值范围内"""
    threshold = expected_value * (threshold_percent / 100)
    lower_bound = expected_value - threshold
    upper_bound = expected_value + threshold
    
    assert lower_bound <= actual_value <= upper_bound, \
        f"性能超出阈值范围: {actual_value} 不在 [{lower_bound}, {upper_bound}] 内"


def assert_memory_usage_reasonable(memory_mb, max_memory_mb=500):
    """断言内存使用合理"""
    assert memory_mb <= max_memory_mb, \
        f"内存使用过多: {memory_mb:.2f}MB > {max_memory_mb}MB"


def assert_no_memory_leak(initial_memory_mb, final_memory_mb, max_leak_mb=50):
    """断言没有内存泄漏"""
    leak = final_memory_mb - initial_memory_mb
    assert leak <= max_leak_mb, \
        f"检测到内存泄漏: {leak:.2f}MB > {max_leak_mb}MB"


# 将断言函数添加到pytest命名空间
pytest.assert_performance_within_threshold = assert_performance_within_threshold
pytest.assert_memory_usage_reasonable = assert_memory_usage_reasonable
pytest.assert_no_memory_leak = assert_no_memory_leak
