"""
性能基准测试
测试系统的各项性能指标
"""

import pytest
import os
import sys
import time
import gc
import logging
from typing import Dict, Any, List, Tuple
import numpy as np
import torch
import psutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils.trainer import Trainer
from torch_game.tests.utils import (
    TestConfig, create_test_config, create_test_env, create_test_agent,
    MockGameAssistant, TestDataGenerator,
    validate_performance_metrics, setup_test_logging,
    measure_execution_time, get_system_info
)


class TestPerformanceBenchmarks:
    """性能基准测试类"""
    
    @classmethod
    def setup_class(cls):
        """类级别的设置"""
        setup_test_logging()
        cls.logger = logging.getLogger(__name__)
        cls.system_info = get_system_info()
        cls.logger.info(f"系统信息: {cls.system_info}")
        
        # 基准配置
        cls.benchmark_config = create_test_config({
            'env.max_steps': 50,
            'env.n_colors': 6,
            'env.storage_capacity': 7,
            'agent.hidden_dim': 128,
            'agent.lr': 3e-4,
            'training.batch_size': 32,
            'training.update_interval': 100
        })
        
        cls.data_generator = TestDataGenerator(seed=42)
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 清理内存
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.env = None
        self.agent = None
        self.trainer = None
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.env:
            self.env.close()
        if self.trainer and hasattr(self.trainer, 'writer'):
            self.trainer.writer.close()
        
        # 清理内存
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    @measure_execution_time
    def test_environment_creation_speed(self):
        """测试环境创建速度"""
        self.logger.info("开始测试环境创建速度...")
        
        creation_times = []
        num_trials = 10
        
        for trial in range(num_trials):
            start_time = time.time()
            
            # 创建环境
            mock_assistant = MockGameAssistant()
            env = OptimizedMatch3Env(
                game_assistant=mock_assistant,
                max_steps=self.benchmark_config.get('env.max_steps'),
                n_colors=self.benchmark_config.get('env.n_colors'),
                storage_capacity=self.benchmark_config.get('env.storage_capacity')
            )
            
            creation_time = time.time() - start_time
            creation_times.append(creation_time)
            
            # 清理
            env.close()
            del env
        
        # 计算统计信息
        avg_time = np.mean(creation_times)
        std_time = np.std(creation_times)
        min_time = np.min(creation_times)
        max_time = np.max(creation_times)
        
        metrics = {
            'avg_creation_time': avg_time,
            'std_creation_time': std_time,
            'min_creation_time': min_time,
            'max_creation_time': max_time,
            'trials': num_trials
        }
        
        # 验证性能
        assert avg_time < 1.0, f"环境创建时间过长: {avg_time:.4f}秒"
        
        self.logger.info(f"环境创建速度测试完成: {metrics}")
        return metrics
    
    @measure_execution_time
    def test_agent_inference_speed(self):
        """测试智能体推理速度"""
        self.logger.info("开始测试智能体推理速度...")
        
        # 创建环境和智能体
        self.env = create_test_env(config=self.benchmark_config)
        self.agent = create_test_agent(env=self.env, config=self.benchmark_config)
        
        # 准备测试数据
        state_dim = np.prod(self.env.observation_space.shape)
        test_states = [
            np.random.random(state_dim).astype(np.float32) 
            for _ in range(100)
        ]
        
        # 预热
        for _ in range(10):
            self.agent.select_action(test_states[0])
        
        # 测试推理速度
        inference_times = []
        
        for state in test_states:
            start_time = time.time()
            action, log_prob = self.agent.select_action(state)
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
        
        # 计算统计信息
        avg_time = np.mean(inference_times)
        std_time = np.std(inference_times)
        throughput = 1.0 / avg_time  # 每秒推理次数
        
        metrics = {
            'avg_inference_time': avg_time,
            'std_inference_time': std_time,
            'throughput_per_second': throughput,
            'total_inferences': len(test_states)
        }
        
        # 验证性能
        assert avg_time < 0.1, f"推理时间过长: {avg_time:.4f}秒"
        assert throughput > 10, f"推理吞吐量过低: {throughput:.2f} inferences/sec"
        
        self.logger.info(f"智能体推理速度测试完成: {metrics}")
        return metrics
    
    @measure_execution_time
    def test_training_step_speed(self):
        """测试训练步骤速度"""
        self.logger.info("开始测试训练步骤速度...")
        
        # 创建智能体
        self.env = create_test_env(config=self.benchmark_config)
        self.agent = create_test_agent(env=self.env, config=self.benchmark_config)
        
        # 生成训练数据
        state_dim = np.prod(self.env.observation_space.shape)
        action_dim = self.env.action_space.n
        batch_size = self.benchmark_config.get('training.batch_size')
        
        training_batches = [
            self.data_generator.generate_batch_data(batch_size, state_dim, action_dim)
            for _ in range(10)
        ]
        
        # 预热
        self.agent.update(training_batches[0])
        
        # 测试训练速度
        update_times = []
        
        for batch in training_batches[1:]:
            start_time = time.time()
            update_info = self.agent.update(batch)
            update_time = time.time() - start_time
            update_times.append(update_time)
        
        # 计算统计信息
        avg_time = np.mean(update_times)
        std_time = np.std(update_times)
        samples_per_second = batch_size / avg_time
        
        metrics = {
            'avg_update_time': avg_time,
            'std_update_time': std_time,
            'samples_per_second': samples_per_second,
            'batch_size': batch_size,
            'total_updates': len(update_times)
        }
        
        # 验证性能
        assert avg_time < 5.0, f"训练更新时间过长: {avg_time:.4f}秒"
        
        self.logger.info(f"训练步骤速度测试完成: {metrics}")
        return metrics
    
    def test_memory_usage_baseline(self):
        """测试基线内存使用"""
        self.logger.info("开始测试基线内存使用...")
        
        process = psutil.Process()
        
        # 记录初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建单个环境和智能体
        self.env = create_test_env(config=self.benchmark_config)
        memory_after_env = process.memory_info().rss / 1024 / 1024
        
        self.agent = create_test_agent(env=self.env, config=self.benchmark_config)
        memory_after_agent = process.memory_info().rss / 1024 / 1024
        
        # 执行一些操作
        observation = self.env.reset()
        for _ in range(10):
            action, _ = self.agent.select_action(observation.reshape(-1))
            observation, _, done, _ = self.env.step(action)
            if done:
                observation = self.env.reset()
        
        memory_after_operations = process.memory_info().rss / 1024 / 1024
        
        metrics = {
            'initial_memory_mb': initial_memory,
            'memory_after_env_mb': memory_after_env,
            'memory_after_agent_mb': memory_after_agent,
            'memory_after_operations_mb': memory_after_operations,
            'env_memory_cost_mb': memory_after_env - initial_memory,
            'agent_memory_cost_mb': memory_after_agent - memory_after_env,
            'operations_memory_cost_mb': memory_after_operations - memory_after_agent
        }
        
        # 验证内存使用
        assert metrics['env_memory_cost_mb'] < 100, f"环境内存使用过多: {metrics['env_memory_cost_mb']:.2f}MB"
        assert metrics['agent_memory_cost_mb'] < 200, f"智能体内存使用过多: {metrics['agent_memory_cost_mb']:.2f}MB"
        
        self.logger.info(f"基线内存使用测试完成: {metrics}")
        return metrics
    
    @pytest.mark.slow
    def test_scalability_benchmark(self):
        """测试可扩展性基准"""
        self.logger.info("开始测试可扩展性基准...")
        
        scalability_results = []
        
        # 测试不同批次大小的性能
        batch_sizes = [8, 16, 32, 64]
        
        for batch_size in batch_sizes:
            self.logger.info(f"测试批次大小: {batch_size}")
            
            # 创建配置
            config = create_test_config({
                'training.batch_size': batch_size,
                'env.max_steps': 30,
                'agent.hidden_dim': 128
            })
            
            # 创建环境和智能体
            env = create_test_env(config=config)
            agent = create_test_agent(env=env, config=config)
            
            # 生成训练数据
            state_dim = np.prod(env.observation_space.shape)
            action_dim = env.action_space.n
            batch_data = self.data_generator.generate_batch_data(
                batch_size, state_dim, action_dim
            )
            
            # 测试训练时间
            start_time = time.time()
            update_info = agent.update(batch_data)
            update_time = time.time() - start_time
            
            # 测试推理时间
            test_state = np.random.random(state_dim).astype(np.float32)
            start_time = time.time()
            action, _ = agent.select_action(test_state)
            inference_time = time.time() - start_time
            
            # 记录结果
            result = {
                'batch_size': batch_size,
                'update_time': update_time,
                'inference_time': inference_time,
                'samples_per_second': batch_size / update_time,
                'policy_loss': update_info.get('policy_loss', 0),
                'value_loss': update_info.get('value_loss', 0)
            }
            
            scalability_results.append(result)
            
            # 清理
            env.close()
            del env, agent
            gc.collect()
        
        # 分析可扩展性
        self.logger.info("可扩展性分析:")
        for result in scalability_results:
            self.logger.info(f"  批次大小 {result['batch_size']}: "
                           f"更新时间 {result['update_time']:.4f}s, "
                           f"推理时间 {result['inference_time']:.4f}s, "
                           f"样本/秒 {result['samples_per_second']:.2f}")
        
        return scalability_results
    
    def test_gpu_vs_cpu_performance(self):
        """测试GPU vs CPU性能对比"""
        self.logger.info("开始测试GPU vs CPU性能对比...")
        
        if not torch.cuda.is_available():
            pytest.skip("GPU不可用，跳过GPU性能测试")
        
        performance_comparison = {}
        devices = ['cpu', 'cuda']
        
        for device in devices:
            self.logger.info(f"测试设备: {device}")
            
            # 创建配置
            config = create_test_config({
                'agent.hidden_dim': 256,  # 使用较大的网络以突出差异
                'training.batch_size': 64
            })
            
            # 创建环境和智能体
            env = create_test_env(config=config)
            agent = create_test_agent(env=env, config=config, device=device)
            
            # 生成测试数据
            state_dim = np.prod(env.observation_space.shape)
            action_dim = env.action_space.n
            batch_data = self.data_generator.generate_batch_data(64, state_dim, action_dim)
            
            # 预热
            for _ in range(3):
                agent.update(batch_data)
            
            # 测试训练性能
            start_time = time.time()
            for _ in range(10):
                agent.update(batch_data)
            training_time = time.time() - start_time
            
            # 测试推理性能
            test_state = np.random.random(state_dim).astype(np.float32)
            start_time = time.time()
            for _ in range(100):
                action, _ = agent.select_action(test_state)
            inference_time = time.time() - start_time
            
            performance_comparison[device] = {
                'training_time': training_time,
                'inference_time': inference_time,
                'training_throughput': 640 / training_time,  # 64 samples * 10 updates
                'inference_throughput': 100 / inference_time
            }
            
            # 清理
            env.close()
            del env, agent
            gc.collect()
            if device == 'cuda':
                torch.cuda.empty_cache()
        
        # 计算加速比
        if 'cpu' in performance_comparison and 'cuda' in performance_comparison:
            training_speedup = (performance_comparison['cpu']['training_time'] / 
                              performance_comparison['cuda']['training_time'])
            inference_speedup = (performance_comparison['cpu']['inference_time'] / 
                                performance_comparison['cuda']['inference_time'])
            
            performance_comparison['speedup'] = {
                'training': training_speedup,
                'inference': inference_speedup
            }
        
        self.logger.info(f"GPU vs CPU性能对比完成: {performance_comparison}")
        return performance_comparison
