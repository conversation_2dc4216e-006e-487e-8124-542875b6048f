#!/usr/bin/env python3
"""
自动化测试运行脚本
支持不同类型的测试运行和报告生成
"""

import os
import sys
import argparse
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


class TestRunner:
    """测试运行器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_dir = project_root / "tests"
        self.reports_dir = project_root / "test_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
    def run_unit_tests(self, verbose: bool = False) -> Dict:
        """运行单元测试"""
        print("🧪 运行单元测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_*.py"),
            "--tb=short",
            "-v" if verbose else "-q",
            "--junitxml=" + str(self.reports_dir / "unit_tests.xml"),
            "--cov=torch_game",
            "--cov-report=html:" + str(self.reports_dir / "coverage_html"),
            "--cov-report=xml:" + str(self.reports_dir / "coverage.xml")
        ]
        
        return self._run_pytest_command(cmd, "unit_tests")
    
    def run_integration_tests(self, verbose: bool = False, use_real_game: bool = False) -> Dict:
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "integration"),
            "--tb=short",
            "-v" if verbose else "-q",
            "--junitxml=" + str(self.reports_dir / "integration_tests.xml")
        ]
        
        if not use_real_game:
            cmd.extend(["-m", "not real_game"])
        
        return self._run_pytest_command(cmd, "integration_tests")
    
    def run_performance_tests(self, verbose: bool = False) -> Dict:
        """运行性能测试"""
        print("⚡ 运行性能测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "performance"),
            "--tb=short",
            "-v" if verbose else "-q",
            "--junitxml=" + str(self.reports_dir / "performance_tests.xml"),
            "--benchmark-json=" + str(self.reports_dir / "benchmark_results.json")
        ]
        
        return self._run_pytest_command(cmd, "performance_tests")
    
    def run_slow_tests(self, verbose: bool = False) -> Dict:
        """运行慢速测试"""
        print("🐌 运行慢速测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "slow",
            "--tb=short",
            "-v" if verbose else "-q",
            "--junitxml=" + str(self.reports_dir / "slow_tests.xml")
        ]
        
        return self._run_pytest_command(cmd, "slow_tests")
    
    def run_all_tests(self, verbose: bool = False, include_slow: bool = False, 
                     use_real_game: bool = False) -> Dict:
        """运行所有测试"""
        print("🚀 运行所有测试...")
        
        results = {}
        
        # 单元测试
        results['unit'] = self.run_unit_tests(verbose)
        
        # 集成测试
        results['integration'] = self.run_integration_tests(verbose, use_real_game)
        
        # 性能测试
        results['performance'] = self.run_performance_tests(verbose)
        
        # 慢速测试（可选）
        if include_slow:
            results['slow'] = self.run_slow_tests(verbose)
        
        return results
    
    def run_smoke_tests(self, verbose: bool = False) -> Dict:
        """运行冒烟测试（快速验证）"""
        print("💨 运行冒烟测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "not slow and not performance",
            "--tb=short",
            "-x",  # 遇到第一个失败就停止
            "-v" if verbose else "-q",
            "--junitxml=" + str(self.reports_dir / "smoke_tests.xml")
        ]
        
        return self._run_pytest_command(cmd, "smoke_tests")
    
    def run_regression_tests(self, baseline_dir: Optional[str] = None, verbose: bool = False) -> Dict:
        """运行回归测试"""
        print("🔄 运行回归测试...")
        
        # 运行所有测试并比较结果
        current_results = self.run_all_tests(verbose=verbose, include_slow=False)
        
        if baseline_dir and os.path.exists(baseline_dir):
            # 与基线结果比较
            baseline_file = os.path.join(baseline_dir, "test_results.json")
            if os.path.exists(baseline_file):
                with open(baseline_file, 'r') as f:
                    baseline_results = json.load(f)
                
                regression_report = self._compare_results(baseline_results, current_results)
                
                # 保存回归报告
                with open(self.reports_dir / "regression_report.json", 'w') as f:
                    json.dump(regression_report, f, indent=2)
                
                return regression_report
        
        return current_results
    
    def _run_pytest_command(self, cmd: List[str], test_type: str) -> Dict:
        """运行pytest命令"""
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 解析输出
            output_lines = result.stdout.split('\n')
            summary_line = [line for line in output_lines if 'passed' in line or 'failed' in line]
            
            test_result = {
                'test_type': test_type,
                'return_code': result.returncode,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'summary': summary_line[-1] if summary_line else "No summary available",
                'success': result.returncode == 0
            }
            
            # 打印结果
            status = "✅ 通过" if test_result['success'] else "❌ 失败"
            print(f"{status} {test_type} - 耗时: {duration:.2f}秒")
            
            if not test_result['success']:
                print(f"错误输出: {result.stderr}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_type} 超时")
            return {
                'test_type': test_type,
                'return_code': -1,
                'duration': 1800,
                'error': 'Timeout',
                'success': False
            }
        except Exception as e:
            print(f"💥 {test_type} 运行出错: {e}")
            return {
                'test_type': test_type,
                'return_code': -1,
                'duration': 0,
                'error': str(e),
                'success': False
            }
    
    def _compare_results(self, baseline: Dict, current: Dict) -> Dict:
        """比较测试结果"""
        regression_report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'regressions': [],
            'improvements': [],
            'new_failures': [],
            'fixed_tests': []
        }
        
        for test_type in current:
            if test_type in baseline:
                baseline_success = baseline[test_type].get('success', False)
                current_success = current[test_type].get('success', False)
                
                if baseline_success and not current_success:
                    regression_report['regressions'].append({
                        'test_type': test_type,
                        'baseline_duration': baseline[test_type].get('duration', 0),
                        'current_duration': current[test_type].get('duration', 0)
                    })
                elif not baseline_success and current_success:
                    regression_report['fixed_tests'].append(test_type)
            else:
                # 新测试
                if not current[test_type].get('success', False):
                    regression_report['new_failures'].append(test_type)
        
        return regression_report
    
    def generate_html_report(self, results: Dict) -> str:
        """生成HTML测试报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Torch Game 测试报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .test-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .success {{ background-color: #d4edda; }}
                .failure {{ background-color: #f8d7da; }}
                .summary {{ font-weight: bold; }}
                pre {{ background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎮 Torch Game 测试报告</h1>
                <p>生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        """
        
        for test_type, result in results.items():
            status_class = "success" if result.get('success', False) else "failure"
            status_icon = "✅" if result.get('success', False) else "❌"
            
            html_content += f"""
            <div class="test-section {status_class}">
                <h2>{status_icon} {test_type.title()} Tests</h2>
                <p class="summary">{result.get('summary', 'No summary')}</p>
                <p>耗时: {result.get('duration', 0):.2f}秒</p>
                
                {f'<h3>错误输出:</h3><pre>{result.get("stderr", "")}</pre>' if result.get('stderr') else ''}
            </div>
            """
        
        html_content += """
        </body>
        </html>
        """
        
        report_file = self.reports_dir / "test_report.html"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(report_file)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Torch Game 自动化测试运行器")
    
    parser.add_argument('--type', choices=['unit', 'integration', 'performance', 'slow', 'all', 'smoke', 'regression'],
                       default='smoke', help='测试类型')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--real-game', action='store_true', help='包含真实游戏测试')
    parser.add_argument('--include-slow', action='store_true', help='包含慢速测试')
    parser.add_argument('--baseline-dir', help='回归测试基线目录')
    parser.add_argument('--generate-report', action='store_true', help='生成HTML报告')
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(project_root)
    
    print(f"🎯 开始运行 {args.type} 测试...")
    print(f"📁 项目根目录: {project_root}")
    print(f"📊 报告目录: {runner.reports_dir}")
    print("-" * 60)
    
    # 运行测试
    start_time = time.time()
    
    if args.type == 'unit':
        results = {'unit': runner.run_unit_tests(args.verbose)}
    elif args.type == 'integration':
        results = {'integration': runner.run_integration_tests(args.verbose, args.real_game)}
    elif args.type == 'performance':
        results = {'performance': runner.run_performance_tests(args.verbose)}
    elif args.type == 'slow':
        results = {'slow': runner.run_slow_tests(args.verbose)}
    elif args.type == 'smoke':
        results = {'smoke': runner.run_smoke_tests(args.verbose)}
    elif args.type == 'regression':
        results = runner.run_regression_tests(args.baseline_dir, args.verbose)
    else:  # all
        results = runner.run_all_tests(args.verbose, args.include_slow, args.real_game)
    
    total_time = time.time() - start_time
    
    # 保存结果
    results_file = runner.reports_dir / "test_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 生成报告
    if args.generate_report:
        report_file = runner.generate_html_report(results)
        print(f"📄 HTML报告已生成: {report_file}")
    
    # 打印总结
    print("-" * 60)
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    print(f"📋 结果已保存到: {results_file}")
    
    # 统计成功/失败
    total_tests = len(results)
    successful_tests = sum(1 for r in results.values() if r.get('success', False))
    
    print(f"📊 测试统计: {successful_tests}/{total_tests} 通过")
    
    if successful_tests == total_tests:
        print("🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("💥 部分测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
