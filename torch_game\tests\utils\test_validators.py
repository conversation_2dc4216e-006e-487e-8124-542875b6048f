"""
测试验证器
提供各种测试结果的验证功能
"""

import numpy as np
import torch
from typing import Dict, Any, List, Tuple, Optional
import logging


def validate_training_results(
    results: Dict[str, Any],
    expected_keys: Optional[List[str]] = None,
    min_episodes: int = 1,
    min_avg_reward: float = -float('inf'),
    max_avg_reward: float = float('inf')
) -> Tuple[bool, List[str]]:
    """
    验证训练结果
    
    Args:
        results: 训练结果字典
        expected_keys: 期望的键列表
        min_episodes: 最小回合数
        min_avg_reward: 最小平均奖励
        max_avg_reward: 最大平均奖励
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查必需的键
    if expected_keys is None:
        expected_keys = ['avg_reward', 'total_episodes', 'avg_length']
    
    for key in expected_keys:
        if key not in results:
            errors.append(f"缺少必需的键: {key}")
    
    # 检查回合数
    if 'total_episodes' in results:
        if results['total_episodes'] < min_episodes:
            errors.append(f"回合数太少: {results['total_episodes']} < {min_episodes}")
    
    # 检查平均奖励
    if 'avg_reward' in results:
        avg_reward = results['avg_reward']
        if avg_reward < min_avg_reward:
            errors.append(f"平均奖励太低: {avg_reward} < {min_avg_reward}")
        if avg_reward > max_avg_reward:
            errors.append(f"平均奖励太高: {avg_reward} > {max_avg_reward}")
    
    # 检查数值类型
    numeric_keys = ['avg_reward', 'total_episodes', 'avg_length']
    for key in numeric_keys:
        if key in results:
            if not isinstance(results[key], (int, float, np.number)):
                errors.append(f"键 {key} 应该是数值类型，实际类型: {type(results[key])}")
    
    return len(errors) == 0, errors


def validate_game_state(
    game_state: Dict[str, Any],
    required_keys: Optional[List[str]] = None,
    min_blocks: int = 0,
    max_blocks: int = 100
) -> Tuple[bool, List[str]]:
    """
    验证游戏状态
    
    Args:
        game_state: 游戏状态字典
        required_keys: 必需的键列表
        min_blocks: 最小方块数
        max_blocks: 最大方块数
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查必需的键
    if required_keys is None:
        required_keys = ['main_blocks', 'storage_blocks']
    
    for key in required_keys:
        if key not in game_state:
            errors.append(f"缺少必需的键: {key}")
    
    # 验证主区域方块
    if 'main_blocks' in game_state:
        main_blocks = game_state['main_blocks']
        if not isinstance(main_blocks, dict):
            errors.append("main_blocks 应该是字典类型")
        else:
            num_main_blocks = len(main_blocks)
            if num_main_blocks < min_blocks:
                errors.append(f"主区域方块数太少: {num_main_blocks} < {min_blocks}")
            if num_main_blocks > max_blocks:
                errors.append(f"主区域方块数太多: {num_main_blocks} > {max_blocks}")
            
            # 验证方块数据结构
            for block_id, block_data in main_blocks.items():
                block_errors = validate_block_data(block_data, block_id)
                errors.extend(block_errors)
    
    # 验证预存区方块
    if 'storage_blocks' in game_state:
        storage_blocks = game_state['storage_blocks']
        if not isinstance(storage_blocks, dict):
            errors.append("storage_blocks 应该是字典类型")
        else:
            # 验证方块数据结构
            for block_id, block_data in storage_blocks.items():
                block_errors = validate_block_data(block_data, block_id)
                errors.extend(block_errors)
    
    return len(errors) == 0, errors


def validate_block_data(
    block_data: Dict[str, Any],
    block_id: str
) -> List[str]:
    """
    验证单个方块数据
    
    Args:
        block_data: 方块数据
        block_id: 方块ID
    
    Returns:
        error_messages
    """
    errors = []
    
    # 检查必需的键
    required_keys = ['color', 'position', 'confidence']
    for key in required_keys:
        if key not in block_data:
            errors.append(f"方块 {block_id} 缺少键: {key}")
    
    # 验证颜色
    if 'color' in block_data:
        if not isinstance(block_data['color'], str):
            errors.append(f"方块 {block_id} 的颜色应该是字符串")
    
    # 验证位置
    if 'position' in block_data:
        position = block_data['position']
        if not isinstance(position, (tuple, list)) or len(position) != 2:
            errors.append(f"方块 {block_id} 的位置应该是长度为2的元组或列表")
        else:
            try:
                x, y = position
                if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
                    errors.append(f"方块 {block_id} 的位置坐标应该是数值")
            except ValueError:
                errors.append(f"方块 {block_id} 的位置格式错误")
    
    # 验证置信度
    if 'confidence' in block_data:
        confidence = block_data['confidence']
        if not isinstance(confidence, (int, float)):
            errors.append(f"方块 {block_id} 的置信度应该是数值")
        elif not 0 <= confidence <= 1:
            errors.append(f"方块 {block_id} 的置信度应该在0-1之间，实际值: {confidence}")
    
    return errors


def validate_observation(
    observation: np.ndarray,
    expected_shape: Optional[Tuple[int, ...]] = None,
    value_range: Tuple[float, float] = (0.0, 1.0)
) -> Tuple[bool, List[str]]:
    """
    验证观察值
    
    Args:
        observation: 观察值数组
        expected_shape: 期望的形状
        value_range: 值的范围
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查类型
    if not isinstance(observation, np.ndarray):
        errors.append(f"观察值应该是numpy数组，实际类型: {type(observation)}")
        return False, errors
    
    # 检查形状
    if expected_shape is not None:
        if observation.shape != expected_shape:
            errors.append(f"观察值形状不匹配，期望: {expected_shape}，实际: {observation.shape}")
    
    # 检查数据类型
    if observation.dtype != np.float32:
        errors.append(f"观察值数据类型应该是float32，实际: {observation.dtype}")
    
    # 检查值范围
    min_val, max_val = value_range
    if np.any(observation < min_val) or np.any(observation > max_val):
        errors.append(f"观察值超出范围 [{min_val}, {max_val}]，实际范围: [{observation.min()}, {observation.max()}]")
    
    # 检查NaN和无穷值
    if np.any(np.isnan(observation)):
        errors.append("观察值包含NaN")
    
    if np.any(np.isinf(observation)):
        errors.append("观察值包含无穷值")
    
    return len(errors) == 0, errors


def validate_action(
    action: int,
    action_space_size: int
) -> Tuple[bool, List[str]]:
    """
    验证动作
    
    Args:
        action: 动作值
        action_space_size: 动作空间大小
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查类型
    if not isinstance(action, (int, np.integer)):
        errors.append(f"动作应该是整数，实际类型: {type(action)}")
    
    # 检查范围
    if not 0 <= action < action_space_size:
        errors.append(f"动作超出范围 [0, {action_space_size})，实际值: {action}")
    
    return len(errors) == 0, errors


def validate_reward(
    reward: float,
    min_reward: float = -100.0,
    max_reward: float = 100.0
) -> Tuple[bool, List[str]]:
    """
    验证奖励值
    
    Args:
        reward: 奖励值
        min_reward: 最小奖励
        max_reward: 最大奖励
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查类型
    if not isinstance(reward, (int, float, np.number)):
        errors.append(f"奖励应该是数值，实际类型: {type(reward)}")
        return False, errors
    
    # 检查范围
    if reward < min_reward or reward > max_reward:
        errors.append(f"奖励超出合理范围 [{min_reward}, {max_reward}]，实际值: {reward}")
    
    # 检查NaN和无穷值
    if np.isnan(reward):
        errors.append("奖励值是NaN")
    
    if np.isinf(reward):
        errors.append("奖励值是无穷")
    
    return len(errors) == 0, errors


def validate_model_output(
    output: torch.Tensor,
    expected_shape: Optional[Tuple[int, ...]] = None,
    check_gradients: bool = True
) -> Tuple[bool, List[str]]:
    """
    验证模型输出
    
    Args:
        output: 模型输出张量
        expected_shape: 期望的形状
        check_gradients: 是否检查梯度
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查类型
    if not isinstance(output, torch.Tensor):
        errors.append(f"模型输出应该是torch.Tensor，实际类型: {type(output)}")
        return False, errors
    
    # 检查形状
    if expected_shape is not None:
        if output.shape != expected_shape:
            errors.append(f"模型输出形状不匹配，期望: {expected_shape}，实际: {output.shape}")
    
    # 检查NaN和无穷值
    if torch.any(torch.isnan(output)):
        errors.append("模型输出包含NaN")
    
    if torch.any(torch.isinf(output)):
        errors.append("模型输出包含无穷值")
    
    # 检查梯度
    if check_gradients and output.requires_grad:
        if output.grad is not None:
            if torch.any(torch.isnan(output.grad)):
                errors.append("模型输出梯度包含NaN")
            if torch.any(torch.isinf(output.grad)):
                errors.append("模型输出梯度包含无穷值")
    
    return len(errors) == 0, errors


def validate_performance_metrics(
    metrics: Dict[str, float],
    thresholds: Optional[Dict[str, Tuple[float, float]]] = None
) -> Tuple[bool, List[str]]:
    """
    验证性能指标
    
    Args:
        metrics: 性能指标字典
        thresholds: 阈值字典，格式为 {metric_name: (min_value, max_value)}
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 默认阈值
    if thresholds is None:
        thresholds = {
            'execution_time': (0.0, 10.0),  # 执行时间：0-10秒
            'memory_usage': (0.0, 1000.0),  # 内存使用：0-1000MB
            'accuracy': (0.0, 1.0),         # 准确率：0-1
            'loss': (0.0, 100.0)            # 损失：0-100
        }
    
    # 验证每个指标
    for metric_name, value in metrics.items():
        # 检查类型
        if not isinstance(value, (int, float, np.number)):
            errors.append(f"指标 {metric_name} 应该是数值，实际类型: {type(value)}")
            continue
        
        # 检查阈值
        if metric_name in thresholds:
            min_val, max_val = thresholds[metric_name]
            if value < min_val or value > max_val:
                errors.append(f"指标 {metric_name} 超出阈值范围 [{min_val}, {max_val}]，实际值: {value}")
        
        # 检查NaN和无穷值
        if np.isnan(value):
            errors.append(f"指标 {metric_name} 是NaN")
        
        if np.isinf(value):
            errors.append(f"指标 {metric_name} 是无穷")
    
    return len(errors) == 0, errors
