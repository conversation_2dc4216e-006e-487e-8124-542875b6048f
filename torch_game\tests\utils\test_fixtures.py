"""
测试夹具和工厂函数
提供创建测试对象的标准化方法
"""

import os
import sys
import tempfile
from typing import Dict, Any, Optional
import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils import ConfigManager
from .test_helpers import TestConfig, MockGameAssistant, TestGameAssistant


def create_test_config(overrides: Optional[Dict[str, Any]] = None) -> TestConfig:
    """创建测试配置"""
    config = TestConfig()
    if overrides:
        config.override(overrides)
    return config


def create_test_env(
    game_assistant=None, 
    config: Optional[TestConfig] = None,
    use_mock: bool = True
) -> OptimizedMatch3Env:
    """创建测试环境"""
    if config is None:
        config = create_test_config()
    
    if game_assistant is None:
        if use_mock:
            game_assistant = MockGameAssistant()
        else:
            test_assistant = TestGameAssistant(use_real_game=True)
            game_assistant = test_assistant.get_assistant()
    
    env_config = config.get('env', {})
    env = OptimizedMatch3Env(
        game_assistant=game_assistant,
        max_steps=env_config.get('max_steps', 20),
        n_colors=env_config.get('n_colors', 6),
        storage_capacity=env_config.get('storage_capacity', 7)
    )
    
    return env


def create_test_agent(
    env: OptimizedMatch3Env = None,
    config: Optional[TestConfig] = None,
    device: str = 'cpu'
) -> PPOAgent:
    """创建测试智能体"""
    if config is None:
        config = create_test_config()
    
    if env is None:
        env = create_test_env(config=config)
    
    agent_config = config.get('agent', {})
    state_dim = np.prod(env.observation_space.shape)
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=agent_config.get('lr', 1e-3),
        gamma=agent_config.get('gamma', 0.99),
        epsilon=agent_config.get('epsilon', 0.2),
        value_coef=agent_config.get('value_coef', 0.5),
        entropy_coef=agent_config.get('entropy_coef', 0.01),
        hidden_dim=agent_config.get('hidden_dim', 64),
        device=device
    )
    
    return agent


def create_test_trainer(
    env: OptimizedMatch3Env = None,
    agent: PPOAgent = None,
    config: Optional[TestConfig] = None
):
    """创建测试训练器"""
    from torch_game.core.utils.trainer import Trainer
    from torch_game.core.utils.config import Config

    if config is None:
        config = create_test_config()

    if env is None:
        env = create_test_env(config=config)

    if agent is None:
        agent = create_test_agent(env=env, config=config)

    # 创建Config对象
    trainer_config = Config()
    trainer_config.config = {
        'training': {
            'total_episodes': config.get('training.total_episodes', 5),
            'update_interval': config.get('training.update_interval', 10),
            'batch_size': config.get('training.batch_size', 8),
            'n_epochs': config.get('training.n_epochs', 2),
            'log_interval': 1,
            'eval_interval': 2,
            'save_interval': 5,
            'save_dir': tempfile.mkdtemp()
        }
    }

    trainer = Trainer(
        agent=agent,
        env=env,
        config=trainer_config
    )

    return trainer


def create_mock_training_data(
    state_dim: int = 50,
    action_dim: int = 100,
    batch_size: int = 8
) -> Dict[str, torch.Tensor]:
    """创建模拟训练数据"""
    return {
        'states': torch.randn(batch_size, state_dim),
        'actions': torch.randint(0, action_dim, (batch_size,)),
        'rewards': torch.randn(batch_size),
        'next_states': torch.randn(batch_size, state_dim),
        'dones': torch.randint(0, 2, (batch_size,)).bool(),
        'log_probs': torch.randn(batch_size),
        'values': torch.randn(batch_size),
        'advantages': torch.randn(batch_size),
        'returns': torch.randn(batch_size)
    }


def create_test_game_state() -> Dict[str, Any]:
    """创建测试游戏状态"""
    return {
        'main_blocks': {
            f'block_{i}': {
                'color': f'color_{i % 6}',
                'position': (100 + (i % 8) * 50, 100 + (i // 8) * 50),
                'confidence': 0.9 + np.random.random() * 0.1
            } for i in range(20)
        },
        'storage_blocks': {
            f'storage_{i}': {
                'color': f'color_{i % 6}',
                'position': (400 + i * 100, 750),
                'confidence': 0.9 + np.random.random() * 0.1
            } for i in range(3)
        },
        'game_state': 'in_game',
        'timestamp': np.random.randint(1000000, 9999999)
    }


def create_test_observation(
    n_colors: int = 6,
    storage_capacity: int = 7
) -> np.ndarray:
    """创建测试观察值"""
    # 主区域统计 (n_colors维)
    main_area_stats = np.random.randint(0, 10, n_colors).astype(np.float32)
    
    # 预存区状态 (storage_capacity * n_colors维)
    storage_state = np.random.randint(0, 2, storage_capacity * n_colors).astype(np.float32)
    
    # 额外信息 (5维)
    extra_info = np.random.random(5).astype(np.float32)
    
    # 合并观察值
    observation = np.concatenate([main_area_stats, storage_state, extra_info])
    
    # 归一化到[0, 1]
    observation = observation / (observation.max() + 1e-8)
    
    return observation


def create_test_episode_data(
    episode_length: int = 10,
    state_dim: int = 50,
    action_dim: int = 100
) -> Dict[str, list]:
    """创建测试回合数据"""
    episode_data = {
        'states': [],
        'actions': [],
        'rewards': [],
        'next_states': [],
        'dones': [],
        'log_probs': [],
        'values': []
    }
    
    for step in range(episode_length):
        episode_data['states'].append(np.random.random(state_dim).astype(np.float32))
        episode_data['actions'].append(np.random.randint(0, action_dim))
        episode_data['rewards'].append(np.random.random() * 10 - 5)  # -5到5的奖励
        episode_data['next_states'].append(np.random.random(state_dim).astype(np.float32))
        episode_data['dones'].append(step == episode_length - 1)
        episode_data['log_probs'].append(np.random.random() - 5)  # 负的对数概率
        episode_data['values'].append(np.random.random() * 10)
    
    return episode_data


def setup_test_environment():
    """设置测试环境"""
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    return {
        'device': device,
        'temp_dir': temp_dir,
        'seed': 42
    }


def cleanup_test_environment(temp_dir: str):
    """清理测试环境"""
    import shutil
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
    except Exception as e:
        print(f"清理测试环境失败: {e}")


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    def generate_batch_data(
        self,
        batch_size: int,
        state_dim: int,
        action_dim: int
    ) -> Dict[str, torch.Tensor]:
        """生成批量数据"""
        return create_mock_training_data(state_dim, action_dim, batch_size)
    
    def generate_episode_sequence(
        self,
        num_episodes: int,
        episode_length: int,
        state_dim: int,
        action_dim: int
    ) -> list:
        """生成多个回合的序列数据"""
        episodes = []
        for _ in range(num_episodes):
            episode = create_test_episode_data(episode_length, state_dim, action_dim)
            episodes.append(episode)
        return episodes
    
    def generate_game_states(self, num_states: int) -> list:
        """生成多个游戏状态"""
        states = []
        for _ in range(num_states):
            state = create_test_game_state()
            states.append(state)
        return states
