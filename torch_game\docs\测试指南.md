# Torch Game 测试指南

## 🎯 概述

本文档提供了 Torch Game 项目的完整测试指南，包括测试类型、运行方法、故障排除等。

## 📋 测试架构

### 测试分类

```
torch_game/tests/
├── unit/                    # 单元测试
│   ├── test_optimized_env.py
│   ├── test_ppo_agent.py
│   ├── test_replay_buffer.py
│   └── test_trainer.py
├── integration/             # 集成测试
│   ├── test_end_to_end.py
│   ├── test_real_game_integration.py
│   └── test_training_pipeline.py
├── performance/             # 性能测试
│   ├── test_performance_benchmarks.py
│   ├── test_memory_profiling.py
│   └── test_speed_benchmarks.py
├── utils/                   # 测试工具
│   ├── test_helpers.py
│   ├── test_fixtures.py
│   └── test_validators.py
└── conftest.py             # pytest配置
```

### 测试标记

- `@pytest.mark.slow`: 慢速测试（通常超过10秒）
- `@pytest.mark.integration`: 集成测试
- `@pytest.mark.performance`: 性能测试
- `@pytest.mark.real_game`: 需要真实游戏环境的测试

## 🚀 快速开始

### 安装测试依赖

```bash
pip install pytest pytest-cov pytest-benchmark pytest-html
```

### 运行基本测试

```bash
# 运行所有快速测试（推荐）
python scripts/run_tests.py --type smoke

# 运行单元测试
python scripts/run_tests.py --type unit

# 运行集成测试
python scripts/run_tests.py --type integration

# 运行性能测试
python scripts/run_tests.py --type performance
```

## 📊 测试类型详解

### 1. 单元测试

**目的**: 测试单个组件的功能

**特点**:
- 快速执行（< 1秒）
- 使用模拟对象
- 高覆盖率

**示例**:
```bash
# 运行环境单元测试
pytest tests/test_optimized_env.py -v

# 运行智能体单元测试
pytest tests/test_ppo_agent.py -v
```

### 2. 集成测试

**目的**: 测试组件间的交互

**特点**:
- 中等执行时间（1-30秒）
- 测试真实交互
- 端到端验证

**示例**:
```bash
# 运行端到端测试
pytest tests/integration/test_end_to_end.py -v

# 运行训练流程测试
pytest tests/integration/test_training_pipeline.py -v
```

### 3. 性能测试

**目的**: 验证系统性能指标

**特点**:
- 较长执行时间
- 基准测试
- 内存分析

**示例**:
```bash
# 运行性能基准测试
pytest tests/performance/test_performance_benchmarks.py -v

# 运行内存分析测试
pytest tests/performance/test_memory_profiling.py -v
```

### 4. 真实游戏测试

**目的**: 与真实游戏环境集成测试

**前置条件**:
- 游戏窗口 "最强祖师" 必须打开
- YOLO模型文件存在: `models/sanxiao/best.pt`

**示例**:
```bash
# 运行真实游戏测试
python scripts/run_tests.py --type integration --real-game
```

## 🛠️ 测试工具使用

### 测试配置

```python
from torch_game.tests.utils import create_test_config

# 创建测试配置
config = create_test_config({
    'env.max_steps': 20,
    'agent.lr': 1e-3,
    'training.total_episodes': 5
})
```

### 模拟对象

```python
from torch_game.tests.utils import MockGameAssistant

# 创建模拟游戏助手
mock_assistant = MockGameAssistant()
mock_assistant.current_state = "in_game"

# 检查调用次数
assert mock_assistant.call_count['click_block'] == 5
```

### 测试夹具

```python
def test_with_fixtures(test_environment, test_agent):
    """使用预配置的测试夹具"""
    observation = test_environment.reset()
    action, _ = test_agent.select_action(observation.reshape(-1))
    assert isinstance(action, int)
```

## 📈 性能基准

### 基准指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 环境创建时间 | < 1秒 | 环境初始化速度 |
| 智能体推理时间 | < 0.1秒 | 单次动作选择时间 |
| 训练更新时间 | < 5秒 | 单次网络更新时间 |
| 内存使用 | < 500MB | 基线内存占用 |

### 运行基准测试

```bash
# 运行完整性能测试
python scripts/run_tests.py --type performance --verbose

# 生成性能报告
python scripts/run_tests.py --type performance --generate-report
```

## 🔧 故障排除

### 常见问题

#### 1. 真实游戏测试失败

**症状**: `pytest.skip("真实游戏环境不可用")`

**解决方案**:
1. 确保游戏窗口 "最强祖师" 已打开
2. 检查模型文件路径: `models/sanxiao/best.pt`
3. 验证游戏窗口可以被检测到

```bash
# 测试游戏窗口检测
python -c "
from assist import AssistBasicToolkit
assist = AssistBasicToolkit()
hwnd = assist.register_and_bind_window_objects('最强祖师')
print(f'游戏窗口句柄: {hwnd}')
"
```

#### 2. 内存测试失败

**症状**: `AssertionError: 内存增长过多`

**解决方案**:
1. 关闭其他占用内存的程序
2. 运行垃圾回收: `gc.collect()`
3. 检查是否有内存泄漏

```python
import gc
import torch
gc.collect()
if torch.cuda.is_available():
    torch.cuda.empty_cache()
```

#### 3. 性能测试超时

**症状**: `subprocess.TimeoutExpired`

**解决方案**:
1. 增加超时时间
2. 使用更小的测试数据集
3. 跳过慢速测试: `pytest -m "not slow"`

#### 4. GPU测试失败

**症状**: `RuntimeError: CUDA out of memory`

**解决方案**:
1. 减少批次大小
2. 使用CPU测试: `--device cpu`
3. 清理GPU内存

```python
import torch
torch.cuda.empty_cache()
```

### 调试技巧

#### 1. 详细日志

```bash
# 启用详细日志
pytest tests/ -v -s --log-cli-level=DEBUG
```

#### 2. 单个测试调试

```bash
# 运行单个测试
pytest tests/test_optimized_env.py::TestOptimizedMatch3Env::test_reset -v -s

# 进入调试模式
pytest tests/test_optimized_env.py::TestOptimizedMatch3Env::test_reset --pdb
```

#### 3. 性能分析

```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 运行测试代码
test_function()

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative').print_stats(10)
```

## 📊 测试报告

### 生成报告

```bash
# 生成HTML报告
python scripts/run_tests.py --type all --generate-report

# 生成覆盖率报告
pytest --cov=torch_game --cov-report=html
```

### 报告位置

- HTML测试报告: `test_reports/test_report.html`
- 覆盖率报告: `test_reports/coverage_html/index.html`
- JUnit XML: `test_reports/*.xml`
- 基准测试结果: `test_reports/benchmark_results.json`

## 🔄 持续集成

### GitHub Actions 配置

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: python scripts/run_tests.py --type smoke
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

### 本地CI模拟

```bash
# 模拟CI环境
python scripts/run_tests.py --type all --verbose --generate-report
```

## 📝 编写测试

### 测试命名规范

```python
class TestComponentName:
    def test_specific_functionality(self):
        """测试特定功能"""
        pass
    
    def test_error_handling(self):
        """测试错误处理"""
        pass
    
    def test_edge_cases(self):
        """测试边界情况"""
        pass
```

### 测试结构

```python
def test_example():
    # Arrange - 准备测试数据
    config = create_test_config()
    env = create_test_env(config=config)
    
    # Act - 执行测试操作
    observation = env.reset()
    
    # Assert - 验证结果
    assert observation is not None
    assert observation.shape == env.observation_space.shape
```

### 最佳实践

1. **独立性**: 每个测试应该独立运行
2. **可重复性**: 测试结果应该一致
3. **快速性**: 单元测试应该快速执行
4. **清晰性**: 测试意图应该明确
5. **覆盖性**: 覆盖主要功能和边界情况

## 🎯 测试策略

### 测试金字塔

```
        /\
       /  \
      /    \     E2E Tests (少量)
     /______\
    /        \
   /          \   Integration Tests (适量)
  /____________\
 /              \
/________________\ Unit Tests (大量)
```

### 测试优先级

1. **高优先级**: 核心功能单元测试
2. **中优先级**: 集成测试和性能测试
3. **低优先级**: 边界情况和压力测试

### 测试覆盖率目标

- 单元测试覆盖率: > 80%
- 集成测试覆盖率: > 60%
- 总体覆盖率: > 75%
