<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="1" skipped="0" tests="2" time="1.095" timestamp="2025-06-26T01:35:55.003280+08:00" hostname="DESKTOP-BKSHHBC"><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_mock_environment_pipeline" time="0.273" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_short_training_pipeline" time="0.513"><failure message="RuntimeError: mat1 and mat2 shapes cannot be multiplied (1x53 and 64x64)">tests\utils\test_helpers.py:232: in wrapper
    result = func(*args, **kwargs)
tests\integration\test_end_to_end.py:109: in test_short_training_pipeline
    self.trainer.train()
core\utils\trainer.py:81: in train
    self.collect_rollout()
core\utils\trainer.py:132: in collect_rollout
    value = self.agent.ac.critic(
D:\soft\Python\Python310\lib\site-packages\torch\nn\modules\module.py:1130: in _call_impl
    return forward_call(*input, **kwargs)
D:\soft\Python\Python310\lib\site-packages\torch\nn\modules\container.py:139: in forward
    input = module(input)
D:\soft\Python\Python310\lib\site-packages\torch\nn\modules\module.py:1130: in _call_impl
    return forward_call(*input, **kwargs)
D:\soft\Python\Python310\lib\site-packages\torch\nn\modules\linear.py:114: in forward
    return F.linear(input, self.weight, self.bias)
E   RuntimeError: mat1 and mat2 shapes cannot be multiplied (1x53 and 64x64)</failure></testcase></testsuite></testsuites>