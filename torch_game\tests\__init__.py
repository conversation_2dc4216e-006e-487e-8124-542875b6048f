# Tests package
"""
Torch Game 测试包

包含完整的测试套件：
- 单元测试：测试单个组件功能
- 集成测试：测试组件间交互
- 性能测试：测试系统性能指标
- 工具模块：测试辅助工具和夹具
"""

# 导入测试工具
from .utils import (
    TestGameAssistant, MockGameAssistant, TestConfig,
    create_test_env, create_test_agent, create_test_config,
    validate_training_results, validate_game_state,
    setup_test_logging
)

# 导入集成测试
from .integration import (
    TestEndToEnd, TestRealGameIntegration, TestTrainingPipeline
)

# 导入性能测试
from .performance import (
    TestPerformanceBenchmarks, TestMemoryProfiling
)

__all__ = [
    # 测试工具
    'TestGameAssistant',
    'MockGameAssistant',
    'TestConfig',
    'create_test_env',
    'create_test_agent',
    'create_test_config',
    'validate_training_results',
    'validate_game_state',
    'setup_test_logging',

    # 集成测试
    'TestEndToEnd',
    'TestRealGameIntegration',
    'TestTrainingPipeline',

    # 性能测试
    'TestPerformanceBenchmarks',
    'TestMemoryProfiling'
]